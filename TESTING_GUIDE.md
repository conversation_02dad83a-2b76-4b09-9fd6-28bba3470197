# AntiCheat Testing Guide

## Test Scenarios for False Positive Fixes

### Movement Tests
1. **Jump on Slime Blocks** - Should not trigger fly hack detection
2. **Swimming/Diving** - Should not trigger any movement violations
3. **Ladder/Vine Climbing** - Should not trigger mid-air jump detection
4. **Ice Walking/Running** - Should not trigger speed detection
5. **Sprinting with Speed Potions** - Should calculate proper speed limits
6. **<PERSON><PERSON> Takeoff** - Should not trigger fly detection
7. **Water/Lava Movement** - Should be properly exempted

### Lag Simulation Tests
1. **High Ping Scenarios** - Test with simulated network lag
2. **Server TPS Drops** - Test during server performance issues
3. **Rapid Movement** - Quick direction changes and stops

### Edge Cases
1. **Knockback from Mobs** - Should not trigger fly detection
2. **Explosion Knockback** - Should handle high velocity properly
3. **Wind Charge Usage** (1.21+) - Should be properly exempted
4. **Scaffolding Building** - Should not trigger violations

### Debug Information
- All violations now include debug messages
- Check server logs for detailed information
- Speed violations show actual vs max allowed speed

## What to Look For
- ✅ No false kicks for legitimate movement
- ✅ Debug messages in chat for testing
- ✅ Proper exemptions for special blocks/conditions
- ✅ Better lag tolerance

## If Issues Persist
1. Check server logs for debug messages
2. Note specific scenarios causing problems
3. Adjust thresholds in the code if needed
