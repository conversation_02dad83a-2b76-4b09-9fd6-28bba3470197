package org;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;

import org.bukkit.entity.Player;
import org.bukkit.Bukkit;
import java.util.*;

public class CombatAC implements Listener {
    private final Map<UUID, Map<String, Integer>> violations = new HashMap<>();
    private final Map<UUID, Long> lastClickTime = new HashMap<>();
    private final Map<UUID, Integer> clickCount = new HashMap<>();
    private final Map<UUID, Long> cpsStartTime = new HashMap<>();
    private final Map<UUID, UUID> lastTarget = new HashMap<>();
    private final Map<UUID, Float> lastYaw = new HashMap<>();
    private final Map<UUID, Long> lastAttackTime = new HashMap<>();
    private final Map<UUID, Integer> crits = new HashMap<>();

    @EventHandler
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof Player) || !(event.getEntity() instanceof Player)) return;
        Player attacker = (Player) event.getDamager();
        Player victim = (Player) event.getEntity();
        if (shouldBypass(attacker)) return;
        UUID uuid = attacker.getUniqueId();

        // --- Reach Check ---
        double distance = attacker.getLocation().distance(victim.getLocation());
        if (distance > 3.5) flag(attacker, "Reach");

        // --- Enhanced KillAura Check ---
        long now = System.currentTimeMillis();
        float yaw = attacker.getLocation().getYaw();
        float pitch = attacker.getLocation().getPitch();
        UUID last = lastTarget.get(uuid);
        Float lastYawVal = lastYaw.get(uuid);
        Long lastAtk = lastAttackTime.get(uuid);

        // Check if attacking from behind (player not looking at victim)
        double attackerYaw = Math.toRadians(yaw);
        double victimYaw = Math.toRadians(victim.getLocation().getYaw());
        double angleDiff = Math.abs(attackerYaw - victimYaw);
        if (angleDiff > Math.PI) angleDiff = 2 * Math.PI - angleDiff;

        // If attacker is behind victim (within 90 degrees of victim's back)
        if (angleDiff < Math.PI / 2) {
            // Calculate if attacker is actually looking at victim
            org.bukkit.util.Vector toVictim = victim.getLocation().toVector().subtract(attacker.getLocation().toVector()).normalize();
            org.bukkit.util.Vector attackerDirection = attacker.getLocation().getDirection();
            double dot = toVictim.dot(attackerDirection);

            // If attacker is not looking at victim but still hitting (dot < 0.5 means not looking directly)
            if (dot < 0.3) {
                attacker.sendMessage("§c[AntiCheat] KillAura detected (hitting from behind)");
                Bukkit.getLogger().info("[AntiCheatAIO] " + attacker.getName() + " KillAura - hitting from behind, dot: " + dot);
                flag(attacker, "KillAura");
            }
        }

        // Original killaura detection (improved)
        if (last != null && !last.equals(victim.getUniqueId()) && lastYawVal != null && lastAtk != null) {
            float yawChange = Math.abs(yaw - lastYawVal);
            long timeBetween = now - lastAtk;

            // More sensitive detection
            if (timeBetween < 100 && yawChange < 5) { // Reduced thresholds
                attacker.sendMessage("§c[AntiCheat] KillAura detected (rapid target switching)");
                Bukkit.getLogger().info("[AntiCheatAIO] " + attacker.getName() + " KillAura - rapid switching, time: " + timeBetween + "ms, yaw change: " + yawChange);
                flag(attacker, "KillAura");
            }

            // Check for impossible head snaps (very large yaw changes in short time)
            if (timeBetween < 200 && yawChange > 90) {
                attacker.sendMessage("§c[AntiCheat] KillAura detected (impossible head movement)");
                flag(attacker, "KillAura");
            }
        }

        lastTarget.put(uuid, victim.getUniqueId());
        lastYaw.put(uuid, yaw);
        lastAttackTime.put(uuid, now);

        // --- AutoClicker Check ---
        long lastClick = lastClickTime.getOrDefault(uuid, 0L);
        int clicks = clickCount.getOrDefault(uuid, 0);
        long cpsStart = cpsStartTime.getOrDefault(uuid, now);
        if (now - cpsStart > 1000) {
            if (clicks > 20) flag(attacker, "AutoClicker"); // Increased CPS threshold
            clickCount.put(uuid, 1);
            cpsStartTime.put(uuid, now);
        } else {
            clickCount.put(uuid, clicks + 1);
        }
        if (lastClick != 0L && now - lastClick < 40) flag(attacker, "AutoClicker"); // Increased minimum time between clicks
        lastClickTime.put(uuid, now);

        // --- Criticals Check ---
        // If every hit is a critical, but player is not jumping or falling
        boolean isCrit = attacker.getFallDistance() > 0.0 && !attacker.isOnGround();
        if (isCrit) {
            int c = crits.getOrDefault(uuid, 0) + 1;
            crits.put(uuid, c);
            if (c > 15) flag(attacker, "Criticals"); // Increased consecutive criticals threshold
        } else {
            crits.put(uuid, 0);
        }
    }

    private void flag(Player player, String check) {
        UUID uuid = player.getUniqueId();
        Map<String, Integer> playerViolations = violations.computeIfAbsent(uuid, k -> new HashMap<>());
        int v = playerViolations.getOrDefault(check, 0) + 1;
        playerViolations.put(check, v);
        if (v == 10) {
            player.sendMessage("§e[AntiCheat] Warning: " + check + " (" + v + ")");
        } else if (v == 20) {
            player.kickPlayer("Kicked for cheating: " + check);
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " kicked for " + check);
        } else if (v >= 40) {
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " would be banned for " + check);
        }
    }

    private boolean shouldBypass(Player player) {
        return player.hasMetadata("bypass_anticheat");
    }
}