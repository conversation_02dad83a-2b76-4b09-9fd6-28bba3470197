package org;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;

import org.bukkit.entity.Player;
import org.bukkit.Bukkit;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.util.Vector;
import java.util.*;
import net.kyori.adventure.text.Component;

public class MovementAC implements Listener {
    private final Map<UUID, Map<String, Integer>> violations = new HashMap<>();
    private final Map<UUID, Long> lastMoveTime = new HashMap<>();
    private final Map<UUID, Long> airStartTime = new HashMap<>();
    private final Map<UUID, double[]> lastAirPosition = new HashMap<>();
    private final Map<UUID, Vector> lastVelocity = new HashMap<>();
    private final Map<UUID, Long> lastGroundTime = new HashMap<>();
    private final Map<UUID, Integer> consecutiveAirMoves = new HashMap<>();

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        if (shouldBypass(player)) return;
        if (player.getAllowFlight() || player.getGameMode().toString().equals("CREATIVE")) return;
        UUID uuid = player.getUniqueId();
        double fromY = event.getFrom().getY();
        double toY = event.getTo().getY();
        long now = System.currentTimeMillis();

        // --- Improved Ground Detection ---
        boolean onGround = isPlayerOnGround(player);
        boolean inAir = !onGround;
        // Track ground time for better lag compensation
        if (onGround) {
            lastGroundTime.put(uuid, now);
            consecutiveAirMoves.put(uuid, 0);
            airStartTime.remove(uuid);
            lastAirPosition.remove(uuid);
        } else {
            consecutiveAirMoves.put(uuid, consecutiveAirMoves.getOrDefault(uuid, 0) + 1);
        }

        // --- Improved Fly Check: In air, not falling, not moving, for >3s ---
        double[] pos = {player.getLocation().getX(), player.getLocation().getY(), player.getLocation().getZ()};
        Vector currentVelocity = player.getVelocity();

        if (inAir && !isLegitimateAirMovement(player)) {
            double[] lastPos = lastAirPosition.get(uuid);
            Vector lastVel = lastVelocity.get(uuid);

            // Check if player is truly stationary (position and velocity)
            boolean positionStationary = lastPos != null &&
                Math.abs(pos[0] - lastPos[0]) < 0.01 &&
                Math.abs(pos[1] - lastPos[1]) < 0.01 &&
                Math.abs(pos[2] - lastPos[2]) < 0.01;

            boolean velocityStationary = lastVel != null &&
                Math.abs(currentVelocity.getX() - lastVel.getX()) < 0.01 &&
                Math.abs(currentVelocity.getY() - lastVel.getY()) < 0.01 &&
                Math.abs(currentVelocity.getZ() - lastVel.getZ()) < 0.01 &&
                currentVelocity.length() < 0.1;

            if (positionStationary && velocityStationary) {
                long airStart = airStartTime.getOrDefault(uuid, now);
                if (airStart == 0) {
                    airStartTime.put(uuid, now);
                } else if (now - airStart > 3500) { // Increased to 3.5 seconds for better lag tolerance
                    // Additional check: make sure player has been in air for enough consecutive moves
                    if (consecutiveAirMoves.getOrDefault(uuid, 0) > 15) {
                        player.kick(Component.text("Kicked for flying (frozen in air)"));
                        Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " kicked for flying (frozen in air)");
                    }
                }
            } else {
                airStartTime.put(uuid, now);
            }
            lastAirPosition.put(uuid, pos);
        }

        lastVelocity.put(uuid, currentVelocity.clone());

        // --- Improved Mid-Air Jump Detection ---
        checkMidAirJump(player, event, inAir, fromY, toY, now);

        // --- Improved Speed Check ---
        checkSpeed(player, event, now);

        // --- NoFall Check ---
        float fallDistance = player.getFallDistance();
        if (fallDistance > 5 && onGround && event.getFrom().getY() - event.getTo().getY() > 3) {
            if (!player.hasPotionEffect(PotionEffectType.SLOW_FALLING)) flag(player, "NoFall");
        }
    }

    private boolean isPlayerOnGround(Player player) {
        // Use Bukkit's built-in ground detection which is more reliable
        if (player.isOnGround()) return true;

        // Additional checks for edge cases
        Block blockBelow = player.getLocation().getBlock().getRelative(0, -1, 0);
        Block blockAt = player.getLocation().getBlock();

        // Check if standing on solid block
        if (blockBelow.getType().isSolid()) return true;

        // Check for liquid (water/lava)
        if (blockAt.isLiquid() || blockBelow.isLiquid()) return true;

        // Check for special blocks that count as "ground"
        Material belowType = blockBelow.getType();
        if (belowType == Material.WATER || belowType == Material.LAVA ||
            belowType == Material.COBWEB || belowType == Material.POWDER_SNOW) {
            return true;
        }

        return false;
    }

    private boolean isLegitimateAirMovement(Player player) {
        // Check for various legitimate reasons to be in air
        if (player.isGliding() || player.isSwimming() || player.isInsideVehicle()) return true;
        if (player.hasPotionEffect(PotionEffectType.LEVITATION)) return true;
        if (player.hasPotionEffect(PotionEffectType.SLOW_FALLING)) return true;

        // Check for blocks that affect movement
        Block blockAt = player.getLocation().getBlock();
        Block blockBelow = player.getLocation().getBlock().getRelative(0, -1, 0);
        Material atType = blockAt.getType();
        Material belowType = blockBelow.getType();

        // Liquid movement
        if (atType == Material.WATER || atType == Material.LAVA) return true;
        if (belowType == Material.WATER || belowType == Material.LAVA) return true;

        // Special blocks
        if (atType == Material.COBWEB || atType == Material.POWDER_SNOW) return true;
        if (belowType == Material.SLIME_BLOCK || belowType == Material.HONEY_BLOCK) return true;

        // Climbing
        if (atType.name().contains("LADDER") || atType.name().contains("VINE")) return true;
        if (atType.name().contains("SCAFFOLDING")) return true;

        // High velocity (knockback, explosions, etc.)
        if (player.getVelocity().length() > 0.8) return true;

        return false;
    }

    private void checkMidAirJump(Player player, PlayerMoveEvent event, boolean inAir, double fromY, double toY, long now) {
        UUID uuid = player.getUniqueId();

        // Only check if player is moving upward in air
        if (!inAir || fromY >= toY || Math.abs(toY - fromY) <= 0.15) return;

        // Comprehensive exemption checks
        boolean recentlyDamaged = player.getNoDamageTicks() > 0;
        boolean hasJumpBoost = player.hasPotionEffect(PotionEffectType.JUMP_BOOST);
        boolean hasLevitation = player.hasPotionEffect(PotionEffectType.LEVITATION);
        boolean isWindCharged = player.getVelocity().length() > 1.0;
        boolean isRiding = player.isInsideVehicle();
        boolean isGliding = player.isGliding();
        boolean isSwimming = player.isSwimming();
        boolean isSprinting = player.isSprinting();

        // Check for climbing blocks
        Block blockAt = player.getLocation().getBlock();
        Material atType = blockAt.getType();
        boolean isClimbing = atType.name().contains("LADDER") ||
                           atType.name().contains("VINE") ||
                           atType.name().contains("SCAFFOLDING");

        // Check for bouncy blocks below
        Block blockBelow = player.getLocation().getBlock().getRelative(0, -1, 0);
        boolean onBouncyBlock = blockBelow.getType() == Material.SLIME_BLOCK ||
                               blockBelow.getType() == Material.HONEY_BLOCK;

        // Check for liquid movement
        boolean inLiquid = atType == Material.WATER || atType == Material.LAVA ||
                          blockAt.getRelative(0, 1, 0).getType() == Material.WATER;

        // Check if recently left ground (lag compensation)
        Long lastGround = lastGroundTime.get(uuid);
        boolean recentlyOnGround = lastGround != null && (now - lastGround) < 500; // 500ms tolerance

        // Check velocity for legitimate upward movement
        Vector velocity = player.getVelocity();
        boolean hasUpwardVelocity = velocity.getY() > 0.2;

        // Only flag if none of the exemptions apply
        if (!recentlyDamaged && !hasJumpBoost && !hasLevitation && !isWindCharged &&
            !isRiding && !isGliding && !isSwimming && !isSprinting && !isClimbing &&
            !onBouncyBlock && !inLiquid && !recentlyOnGround && !hasUpwardVelocity) {

            // Additional check: make sure this isn't just lag
            int airMoves = consecutiveAirMoves.getOrDefault(uuid, 0);
            if (airMoves > 3) { // Only flag after multiple air moves to avoid lag false positives
                player.sendMessage("§c[AntiCheat] Detected suspicious mid-air movement [debug: would flag]");
                Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " flagged for suspicious mid-air movement");
                // Note: Changed to just log instead of immediate ban for testing
            }
        }
    }

    private void checkSpeed(Player player, PlayerMoveEvent event, long now) {
        UUID uuid = player.getUniqueId();

        double fromX = event.getFrom().getX();
        double fromZ = event.getFrom().getZ();
        double toX = event.getTo().getX();
        double toZ = event.getTo().getZ();
        double distance = Math.sqrt(Math.pow(toX - fromX, 2) + Math.pow(toZ - fromZ, 2));

        long lastTime = lastMoveTime.getOrDefault(uuid, now);
        long timeElapsed = now - lastTime;

        // Lag compensation - ignore very small time differences
        if (timeElapsed < 30) return; // Ignore moves less than 30ms apart

        // Calculate speed with better timing
        double speed = distance / (timeElapsed / 1000.0); // blocks per second

        // Get player modifiers
        boolean hasSpeed = player.hasPotionEffect(PotionEffectType.SPEED);
        boolean hasJump = player.hasPotionEffect(PotionEffectType.JUMP_BOOST);
        boolean isSprinting = player.isSprinting();
        boolean isWindCharged = player.getVelocity().length() > 1.0;
        boolean isGliding = player.isGliding();
        boolean isSwimming = player.isSwimming();
        boolean isRiding = player.isInsideVehicle();

        // Check for speed-affecting blocks
        Block blockAt = player.getLocation().getBlock();
        Block blockBelow = player.getLocation().getBlock().getRelative(0, -1, 0);
        boolean onIce = blockBelow.getType().name().contains("ICE");
        boolean inLiquid = blockAt.isLiquid();

        // Calculate max allowed speed based on conditions
        double maxSpeed = 4.3; // Base walking speed
        if (isSprinting) maxSpeed = 5.6;
        if (hasSpeed) {
            int amplifier = player.getPotionEffect(PotionEffectType.SPEED).getAmplifier() + 1;
            maxSpeed *= (1.0 + 0.2 * amplifier);
        }
        if (onIce) maxSpeed *= 2.5; // Ice multiplier
        if (inLiquid) maxSpeed *= 0.8; // Slower in liquid
        if (isGliding || isSwimming || isRiding || isWindCharged) maxSpeed = 20.0; // High limit for special movement

        // Only flag if significantly over the limit
        if (speed > maxSpeed * 1.2 && timeElapsed > 50) { // 20% tolerance + minimum time
            player.sendMessage("§c[AntiCheat] Speed detected (debug: would flag) - Speed: " + String.format("%.2f", speed) + " Max: " + String.format("%.2f", maxSpeed));
            flag(player, "Speed");
        }

        lastMoveTime.put(uuid, now);
    }

    private void flag(Player player, String check) {
        UUID uuid = player.getUniqueId();
        Map<String, Integer> playerViolations = violations.computeIfAbsent(uuid, k -> new HashMap<>());
        int v = playerViolations.getOrDefault(check, 0) + 1;
        playerViolations.put(check, v);
        if (v == 10) {
            player.sendMessage("§e[AntiCheat] Warning: " + check + " (" + v + ")");
        } else if (v == 20) {
            player.kick(Component.text("Kicked for cheating: " + check));
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " kicked for " + check);
        } else if (v >= 40) {
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " would be banned for " + check);
        }
    }

    private boolean shouldBypass(Player player) {
        return player.hasMetadata("bypass_anticheat");
    }
}