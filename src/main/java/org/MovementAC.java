package org;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;

import org.bukkit.entity.Player;
import org.bukkit.Bukkit;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.util.Vector;
import java.util.*;
import net.kyori.adventure.text.Component;

public class MovementAC implements Listener {
    private final Map<UUID, Map<String, Integer>> violations = new HashMap<>();
    private final Map<UUID, Long> lastMoveTime = new HashMap<>();
    private final Map<UUID, Long> airStartTime = new HashMap<>();
    private final Map<UUID, double[]> lastAirPosition = new HashMap<>();
    private final Map<UUID, Vector> lastVelocity = new HashMap<>();
    private final Map<UUID, Long> lastGroundTime = new HashMap<>();
    private final Map<UUID, Integer> consecutiveAirMoves = new HashMap<>();
    private final Map<UUID, Double> highestY = new HashMap<>();
    private final Map<UUID, Boolean> isFalling = new HashMap<>();

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        if (shouldBypass(player)) return;
        if (player.getAllowFlight() || player.getGameMode().toString().equals("CREATIVE")) return;
        UUID uuid = player.getUniqueId();
        double fromY = event.getFrom().getY();
        double toY = event.getTo().getY();
        long now = System.currentTimeMillis();

        // --- Improved Ground Detection ---
        boolean onGround = isPlayerOnGround(player);
        boolean inAir = !onGround;

        // Debug logging (remove this later if too spammy)
        if (inAir) {
            int airMoves = consecutiveAirMoves.getOrDefault(uuid, 0);
            boolean isPlayerFalling = isFalling.getOrDefault(uuid, false);
            if (airMoves % 10 == 0 && airMoves > 0) { // Log every 10 air moves
                Bukkit.getLogger().info("[AntiCheatAIO] DEBUG: " + player.getName() + " has been in air for " + airMoves + " moves, falling: " + isPlayerFalling + ", Y velocity: " + currentVelocity.getY());
            }
        }
        // Track ground time and falling state for better lag compensation
        if (onGround) {
            lastGroundTime.put(uuid, now);
            consecutiveAirMoves.put(uuid, 0);
            airStartTime.remove(uuid);
            lastAirPosition.remove(uuid);
            highestY.remove(uuid);
            isFalling.put(uuid, false);
        } else {
            consecutiveAirMoves.put(uuid, consecutiveAirMoves.getOrDefault(uuid, 0) + 1);

            // Track highest Y position and falling state
            double currentY = player.getLocation().getY();
            double maxY = highestY.getOrDefault(uuid, currentY);

            if (currentY > maxY) {
                highestY.put(uuid, currentY);
                isFalling.put(uuid, false);
            } else if (currentY < maxY - 1.0) { // Started falling (1 block drop)
                isFalling.put(uuid, true);
            }
        }

        // --- Improved Fly Check: In air, not falling, not moving, for >3s ---
        double[] pos = {player.getLocation().getX(), player.getLocation().getY(), player.getLocation().getZ()};
        Vector currentVelocity = player.getVelocity();

        if (inAir && !isLegitimateAirMovement(player)) {
            double[] lastPos = lastAirPosition.get(uuid);
            boolean isPlayerFalling = isFalling.getOrDefault(uuid, false);

            // Don't flag players who are legitimately falling
            if (isPlayerFalling && currentVelocity.getY() < -0.2) {
                // Player is falling legitimately, reset air start time
                airStartTime.put(uuid, now);
                lastAirPosition.put(uuid, pos);
                return; // Skip flight detection for falling players
            }

            // Check if player is stationary in position (less strict than before)
            boolean positionStationary = lastPos != null &&
                Math.abs(pos[0] - lastPos[0]) < 0.05 &&
                Math.abs(pos[1] - lastPos[1]) < 0.05 &&
                Math.abs(pos[2] - lastPos[2]) < 0.05;

            // Also check for hovering (small Y movement but staying in air)
            boolean hovering = lastPos != null &&
                Math.abs(pos[0] - lastPos[0]) < 0.3 &&
                Math.abs(pos[2] - lastPos[2]) < 0.3 &&
                Math.abs(currentVelocity.getY()) < 0.1;

            if (positionStationary || hovering) {
                long airStart = airStartTime.getOrDefault(uuid, now);
                if (airStart == 0) {
                    airStartTime.put(uuid, now);
                } else if (now - airStart > 2000) { // Reduced back to 2 seconds for better detection
                    // Additional check: make sure player has been in air for enough consecutive moves
                    if (consecutiveAirMoves.getOrDefault(uuid, 0) > 8) { // Reduced threshold
                        player.sendMessage("§c[AntiCheat] Flying detected - you will be kicked in 3 seconds");
                        Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " detected flying");

                        // Kick after additional time to allow for lag
                        if (now - airStart > 5000) {
                            player.kick(Component.text("Kicked for flying"));
                            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " kicked for flying");
                        }
                    }
                }
            } else {
                airStartTime.put(uuid, now);
            }
            lastAirPosition.put(uuid, pos);
        }

        lastVelocity.put(uuid, currentVelocity.clone());

        // --- Additional Flight Detection (Moving Flight) ---
        checkMovingFlight(player, inAir, fromY, toY, now);

        // --- Improved Mid-Air Jump Detection ---
        checkMidAirJump(player, event, inAir, fromY, toY, now);

        // --- Improved Speed Check ---
        checkSpeed(player, event, now);

        // --- NoFall Check ---
        float fallDistance = player.getFallDistance();
        if (fallDistance > 5 && onGround && event.getFrom().getY() - event.getTo().getY() > 3) {
            if (!player.hasPotionEffect(PotionEffectType.SLOW_FALLING)) flag(player, "NoFall");
        }
    }

    private boolean isPlayerOnGround(Player player) {
        // Use Bukkit's built-in ground detection which is more reliable
        if (player.isOnGround()) return true;

        // Additional checks for edge cases
        Block blockBelow = player.getLocation().getBlock().getRelative(0, -1, 0);
        Block blockAt = player.getLocation().getBlock();

        // Check if standing on solid block
        if (blockBelow.getType().isSolid()) return true;

        // Check for liquid (water/lava)
        if (blockAt.isLiquid() || blockBelow.isLiquid()) return true;

        // Check for special blocks that count as "ground"
        Material belowType = blockBelow.getType();
        if (belowType == Material.WATER || belowType == Material.LAVA ||
            belowType == Material.COBWEB || belowType == Material.POWDER_SNOW) {
            return true;
        }

        return false;
    }

    private boolean isLegitimateAirMovement(Player player) {
        // Check for various legitimate reasons to be in air
        if (player.isGliding() || player.isSwimming() || player.isInsideVehicle()) return true;
        if (player.hasPotionEffect(PotionEffectType.LEVITATION)) return true;
        if (player.hasPotionEffect(PotionEffectType.SLOW_FALLING)) return true;

        // Check for blocks that affect movement
        Block blockAt = player.getLocation().getBlock();
        Block blockBelow = player.getLocation().getBlock().getRelative(0, -1, 0);
        Material atType = blockAt.getType();
        Material belowType = blockBelow.getType();

        // Liquid movement
        if (atType == Material.WATER || atType == Material.LAVA) return true;
        if (belowType == Material.WATER || belowType == Material.LAVA) return true;

        // Special blocks
        if (atType == Material.COBWEB || atType == Material.POWDER_SNOW) return true;
        if (belowType == Material.SLIME_BLOCK || belowType == Material.HONEY_BLOCK) return true;

        // Climbing
        if (atType.name().contains("LADDER") || atType.name().contains("VINE")) return true;
        if (atType.name().contains("SCAFFOLDING")) return true;

        // High velocity (knockback, explosions, etc.) - but not sustained high velocity
        Vector velocity = player.getVelocity();
        if (velocity.length() > 2.0) return true; // Only very high velocity (explosions, etc.)

        // Check for recent damage (knockback)
        if (player.getNoDamageTicks() > 0) return true;

        return false;
    }

    private void checkMovingFlight(Player player, boolean inAir, double fromY, double toY, long now) {
        UUID uuid = player.getUniqueId();

        // Check for sustained air time with movement (moving flight hacks)
        if (inAir && !isLegitimateAirMovement(player)) {
            int airMoves = consecutiveAirMoves.getOrDefault(uuid, 0);
            boolean isPlayerFalling = isFalling.getOrDefault(uuid, false);

            // Don't flag players who are legitimately falling from height
            if (isPlayerFalling) {
                Vector velocity = player.getVelocity();
                if (velocity.getY() < -0.2) { // Actually falling downward
                    return; // Skip flight detection
                }
            }

            // If player has been in air for many moves, check for flight patterns
            if (airMoves > 20) { // 20+ consecutive air moves
                Vector velocity = player.getVelocity();

                // Check for flight patterns:
                // 1. Sustained horizontal movement in air without falling
                // 2. Upward movement without jump boost
                // 3. Zero or very low Y velocity while moving horizontally

                boolean sustainedHorizontal = Math.abs(velocity.getX()) > 0.1 || Math.abs(velocity.getZ()) > 0.1;
                boolean notFalling = velocity.getY() > -0.3; // Should be falling faster after 20+ moves
                boolean suspiciousUpward = velocity.getY() > 0.1 && !player.hasPotionEffect(PotionEffectType.JUMP_BOOST);

                // Additional check: if player is supposed to be falling but isn't
                boolean shouldBeFalling = !isPlayerFalling && airMoves > 30;

                if ((sustainedHorizontal && notFalling) || suspiciousUpward || shouldBeFalling) {
                    player.sendMessage("§c[AntiCheat] Suspicious flight pattern detected");
                    Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " suspicious flight pattern - Air moves: " + airMoves + " Velocity: " + velocity + " Falling: " + isPlayerFalling);
                    flag(player, "Flight");
                }
            }

            // Extreme air time check (but not if legitimately falling)
            if (airMoves > 40 && !isPlayerFalling) {
                player.sendMessage("§c[AntiCheat] Excessive air time detected");
                Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " excessive air time - " + airMoves + " moves");
                flag(player, "Flight");
            }
        }
    }

    private void checkMidAirJump(Player player, PlayerMoveEvent event, boolean inAir, double fromY, double toY, long now) {
        UUID uuid = player.getUniqueId();

        // Only check if player is moving upward in air
        if (!inAir || fromY >= toY || Math.abs(toY - fromY) <= 0.15) return;

        // Comprehensive exemption checks
        boolean recentlyDamaged = player.getNoDamageTicks() > 0;
        boolean hasJumpBoost = player.hasPotionEffect(PotionEffectType.JUMP_BOOST);
        boolean hasLevitation = player.hasPotionEffect(PotionEffectType.LEVITATION);
        boolean isWindCharged = player.getVelocity().length() > 1.0;
        boolean isRiding = player.isInsideVehicle();
        boolean isGliding = player.isGliding();
        boolean isSwimming = player.isSwimming();
        boolean isSprinting = player.isSprinting();

        // Check for climbing blocks
        Block blockAt = player.getLocation().getBlock();
        Material atType = blockAt.getType();
        boolean isClimbing = atType.name().contains("LADDER") ||
                           atType.name().contains("VINE") ||
                           atType.name().contains("SCAFFOLDING");

        // Check for bouncy blocks below
        Block blockBelow = player.getLocation().getBlock().getRelative(0, -1, 0);
        boolean onBouncyBlock = blockBelow.getType() == Material.SLIME_BLOCK ||
                               blockBelow.getType() == Material.HONEY_BLOCK;

        // Check for liquid movement
        boolean inLiquid = atType == Material.WATER || atType == Material.LAVA ||
                          blockAt.getRelative(0, 1, 0).getType() == Material.WATER;

        // Check if recently left ground (lag compensation)
        Long lastGround = lastGroundTime.get(uuid);
        boolean recentlyOnGround = lastGround != null && (now - lastGround) < 500; // 500ms tolerance

        // Check velocity for legitimate upward movement
        Vector velocity = player.getVelocity();
        boolean hasUpwardVelocity = velocity.getY() > 0.2;

        // Only flag if none of the exemptions apply
        if (!recentlyDamaged && !hasJumpBoost && !hasLevitation && !isWindCharged &&
            !isRiding && !isGliding && !isSwimming && !isSprinting && !isClimbing &&
            !onBouncyBlock && !inLiquid && !recentlyOnGround && !hasUpwardVelocity) {

            // Additional check: make sure this isn't just lag
            int airMoves = consecutiveAirMoves.getOrDefault(uuid, 0);
            if (airMoves > 3) { // Only flag after multiple air moves to avoid lag false positives
                player.sendMessage("§c[AntiCheat] Detected suspicious mid-air movement [debug: would flag]");
                Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " flagged for suspicious mid-air movement");
                // Note: Changed to just log instead of immediate ban for testing
            }
        }
    }

    private void checkSpeed(Player player, PlayerMoveEvent event, long now) {
        UUID uuid = player.getUniqueId();

        double fromX = event.getFrom().getX();
        double fromZ = event.getFrom().getZ();
        double toX = event.getTo().getX();
        double toZ = event.getTo().getZ();
        double distance = Math.sqrt(Math.pow(toX - fromX, 2) + Math.pow(toZ - fromZ, 2));

        long lastTime = lastMoveTime.getOrDefault(uuid, now);
        long timeElapsed = now - lastTime;

        // Lag compensation - ignore very small time differences
        if (timeElapsed < 30) return; // Ignore moves less than 30ms apart

        // Calculate speed with better timing
        double speed = distance / (timeElapsed / 1000.0); // blocks per second

        // Get player modifiers
        boolean hasSpeed = player.hasPotionEffect(PotionEffectType.SPEED);
        boolean hasJump = player.hasPotionEffect(PotionEffectType.JUMP_BOOST);
        boolean isSprinting = player.isSprinting();
        boolean isWindCharged = player.getVelocity().length() > 1.0;
        boolean isGliding = player.isGliding();
        boolean isSwimming = player.isSwimming();
        boolean isRiding = player.isInsideVehicle();

        // Check for speed-affecting blocks
        Block blockAt = player.getLocation().getBlock();
        Block blockBelow = player.getLocation().getBlock().getRelative(0, -1, 0);
        boolean onIce = blockBelow.getType().name().contains("ICE");
        boolean inLiquid = blockAt.isLiquid();

        // Calculate max allowed speed based on conditions
        double maxSpeed = 4.3; // Base walking speed
        if (isSprinting) maxSpeed = 5.6;
        if (hasSpeed) {
            int amplifier = player.getPotionEffect(PotionEffectType.SPEED).getAmplifier() + 1;
            maxSpeed *= (1.0 + 0.2 * amplifier);
        }
        if (onIce) maxSpeed *= 2.5; // Ice multiplier
        if (inLiquid) maxSpeed *= 0.8; // Slower in liquid
        if (isGliding || isSwimming || isRiding || isWindCharged) maxSpeed = 20.0; // High limit for special movement

        // Only flag if significantly over the limit
        if (speed > maxSpeed * 1.2 && timeElapsed > 50) { // 20% tolerance + minimum time
            player.sendMessage("§c[AntiCheat] Speed detected (debug: would flag) - Speed: " + String.format("%.2f", speed) + " Max: " + String.format("%.2f", maxSpeed));
            flag(player, "Speed");
        }

        lastMoveTime.put(uuid, now);
    }

    private void flag(Player player, String check) {
        UUID uuid = player.getUniqueId();
        Map<String, Integer> playerViolations = violations.computeIfAbsent(uuid, k -> new HashMap<>());
        int v = playerViolations.getOrDefault(check, 0) + 1;
        playerViolations.put(check, v);
        if (v == 10) {
            player.sendMessage("§e[AntiCheat] Warning: " + check + " (" + v + ")");
        } else if (v == 20) {
            player.kick(Component.text("Kicked for cheating: " + check));
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " kicked for " + check);
        } else if (v >= 40) {
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " would be banned for " + check);
        }
    }

    private boolean shouldBypass(Player player) {
        return player.hasMetadata("bypass_anticheat");
    }
}