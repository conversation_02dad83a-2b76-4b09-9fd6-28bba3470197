package org;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;

import org.bukkit.entity.Player;
import org.bukkit.Bukkit;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.util.Vector;
import org.bukkit.potion.PotionEffect;
import java.util.*;
import net.kyori.adventure.text.Component;

public class MovementAC implements Listener {
    private final Map<UUID, Map<String, Integer>> violations = new HashMap<>();
    private final Map<UUID, Long> lastMoveTime = new HashMap<>();
    private final Map<UUID, Long> airStartTime = new HashMap<>();
    private final Map<UUID, double[]> lastAirPosition = new HashMap<>();
    private final Map<UUID, Vector> lastVelocity = new HashMap<>();
    private final Map<UUID, Long> lastGroundTime = new HashMap<>();
    private final Map<UUID, Integer> consecutiveAirMoves = new HashMap<>();
    private final Map<UUID, Double> highestY = new HashMap<>();
    private final Map<UUID, Boolean> isFalling = new HashMap<>();
    private final Map<UUID, List<Double>> recentSpeeds = new HashMap<>();
    private final Map<UUID, Long> lastSpeedViolation = new HashMap<>();

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        if (player == null || shouldBypass(player)) return;
        if (player.getAllowFlight() || player.getGameMode().toString().equals("CREATIVE")) return;

        // Null checks for event locations
        if (event.getFrom() == null || event.getTo() == null) return;

        UUID uuid = player.getUniqueId();
        double fromY = event.getFrom().getY();
        double toY = event.getTo().getY();
        long now = System.currentTimeMillis();

        // --- Improved Ground Detection ---
        boolean onGround = isPlayerOnGround(player);
        boolean inAir = !onGround;
        Vector currentVelocity = player.getVelocity();

        // Debug logging (remove this later if too spammy)
        if (inAir) {
            int airMoves = consecutiveAirMoves.getOrDefault(uuid, 0);
            boolean isPlayerFalling = isFalling.getOrDefault(uuid, false);
            if (airMoves % 10 == 0 && airMoves > 0) { // Log every 10 air moves
                Bukkit.getLogger().info("[AntiCheatAIO] DEBUG: " + player.getName() + " has been in air for " + airMoves + " moves, falling: " + isPlayerFalling + ", Y velocity: " + currentVelocity.getY());
            }
        }
        // Track ground time and falling state for better lag compensation
        if (onGround) {
            lastGroundTime.put(uuid, now);
            consecutiveAirMoves.put(uuid, 0);
            airStartTime.remove(uuid);
            lastAirPosition.remove(uuid);
            highestY.remove(uuid);
            isFalling.put(uuid, false);
        } else {
            consecutiveAirMoves.put(uuid, consecutiveAirMoves.getOrDefault(uuid, 0) + 1);

            // Track highest Y position and falling state
            double currentY = player.getLocation().getY();
            double maxY = highestY.getOrDefault(uuid, currentY);

            if (currentY > maxY) {
                highestY.put(uuid, currentY);
                isFalling.put(uuid, false);
            } else if (currentY < maxY - 1.0) { // Started falling (1 block drop)
                isFalling.put(uuid, true);
            }
        }

        // --- Improved Fly Check: In air, not falling, not moving, for >3s ---
        double[] pos = {player.getLocation().getX(), player.getLocation().getY(), player.getLocation().getZ()};

        if (inAir && !isLegitimateAirMovement(player)) {
            double[] lastPos = lastAirPosition.get(uuid);
            boolean isPlayerFalling = isFalling.getOrDefault(uuid, false);

            // Don't flag players who are legitimately falling
            if (isPlayerFalling && currentVelocity.getY() < -0.2) {
                // Player is falling legitimately, reset air start time
                airStartTime.put(uuid, now);
                lastAirPosition.put(uuid, pos);
                return; // Skip flight detection for falling players
            }

            // Check if player is stationary in position (less strict than before)
            boolean positionStationary = lastPos != null &&
                Math.abs(pos[0] - lastPos[0]) < 0.05 &&
                Math.abs(pos[1] - lastPos[1]) < 0.05 &&
                Math.abs(pos[2] - lastPos[2]) < 0.05;

            // Also check for hovering (small Y movement but staying in air)
            boolean hovering = lastPos != null &&
                Math.abs(pos[0] - lastPos[0]) < 0.3 &&
                Math.abs(pos[2] - lastPos[2]) < 0.3 &&
                Math.abs(currentVelocity.getY()) < 0.1;

            if (positionStationary || hovering) {
                long airStart = airStartTime.getOrDefault(uuid, now);
                if (airStart == 0) {
                    airStartTime.put(uuid, now);
                } else if (now - airStart > 2000) { // Reduced back to 2 seconds for better detection
                    // Additional check: make sure player has been in air for enough consecutive moves
                    if (consecutiveAirMoves.getOrDefault(uuid, 0) > 8) { // Reduced threshold
                        player.sendMessage("§c[AntiCheat] Flying detected - you will be kicked in 3 seconds");
                        Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " detected flying");

                        // Kick after additional time to allow for lag
                        if (now - airStart > 5000) {
                            player.kick(Component.text("Kicked for flying"));
                            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " kicked for flying");
                        }
                    }
                }
            } else {
                airStartTime.put(uuid, now);
            }
            lastAirPosition.put(uuid, pos);
        }

        lastVelocity.put(uuid, currentVelocity.clone());

        // --- Additional Flight Detection (Moving Flight) ---
        checkMovingFlight(player, inAir, fromY, toY, now);

        // --- Improved Mid-Air Jump Detection ---
        checkMidAirJump(player, event, inAir, fromY, toY, now);

        // --- Improved Speed Check ---
        checkSpeed(player, event, now);

        // --- NoFall Check ---
        float fallDistance = player.getFallDistance();
        if (fallDistance > 5 && onGround && event.getFrom().getY() - event.getTo().getY() > 3) {
            if (!player.hasPotionEffect(PotionEffectType.SLOW_FALLING)) flag(player, "NoFall");
        }
    }

    private boolean isPlayerOnGround(Player player) {
        // Use Bukkit's built-in ground detection which is more reliable
        if (player.isOnGround()) return true;

        // Additional checks for edge cases
        Block blockBelow = player.getLocation().getBlock().getRelative(0, -1, 0);
        Block blockAt = player.getLocation().getBlock();

        // Check if standing on solid block
        if (blockBelow.getType().isSolid()) return true;

        // Check for liquid (water/lava)
        if (blockAt.isLiquid() || blockBelow.isLiquid()) return true;

        // Check for special blocks that count as "ground"
        Material belowType = blockBelow.getType();
        if (belowType == Material.WATER || belowType == Material.LAVA ||
            belowType == Material.COBWEB || belowType == Material.POWDER_SNOW) {
            return true;
        }

        return false;
    }

    private boolean isLegitimateAirMovement(Player player) {
        // Check for various legitimate reasons to be in air
        if (player.isGliding() || player.isSwimming() || player.isInsideVehicle()) return true;
        if (player.hasPotionEffect(PotionEffectType.LEVITATION)) return true;
        if (player.hasPotionEffect(PotionEffectType.SLOW_FALLING)) return true;

        // Check for blocks that affect movement
        Block blockAt = player.getLocation().getBlock();
        Block blockBelow = player.getLocation().getBlock().getRelative(0, -1, 0);
        Material atType = blockAt.getType();
        Material belowType = blockBelow.getType();

        // Liquid movement
        if (atType == Material.WATER || atType == Material.LAVA) return true;
        if (belowType == Material.WATER || belowType == Material.LAVA) return true;

        // Special blocks
        if (atType == Material.COBWEB || atType == Material.POWDER_SNOW) return true;
        if (belowType == Material.SLIME_BLOCK || belowType == Material.HONEY_BLOCK) return true;

        // Climbing
        if (atType.name().contains("LADDER") || atType.name().contains("VINE")) return true;
        if (atType.name().contains("SCAFFOLDING")) return true;

        // High velocity (knockback, explosions, etc.) - but not sustained high velocity
        Vector velocity = player.getVelocity();
        if (velocity.length() > 2.0) return true; // Only very high velocity (explosions, etc.)

        // Check for recent damage (knockback)
        if (player.getNoDamageTicks() > 0) return true;

        return false;
    }

    private void checkMovingFlight(Player player, boolean inAir, double fromY, double toY, long now) {
        UUID uuid = player.getUniqueId();

        // Check for sustained air time with movement (moving flight hacks)
        if (inAir && !isLegitimateAirMovement(player)) {
            int airMoves = consecutiveAirMoves.getOrDefault(uuid, 0);
            boolean isPlayerFalling = isFalling.getOrDefault(uuid, false);

            // Don't flag players who are legitimately falling from height
            if (isPlayerFalling) {
                Vector velocity = player.getVelocity();
                if (velocity.getY() < -0.2) { // Actually falling downward
                    return; // Skip flight detection
                }
            }

            // If player has been in air for many moves, check for flight patterns
            if (airMoves > 20) { // 20+ consecutive air moves
                Vector velocity = player.getVelocity();

                // Check for flight patterns:
                // 1. Sustained horizontal movement in air without falling
                // 2. Upward movement without jump boost
                // 3. Zero or very low Y velocity while moving horizontally

                boolean sustainedHorizontal = Math.abs(velocity.getX()) > 0.1 || Math.abs(velocity.getZ()) > 0.1;
                boolean notFalling = velocity.getY() > -0.3; // Should be falling faster after 20+ moves
                boolean suspiciousUpward = velocity.getY() > 0.1 && !player.hasPotionEffect(PotionEffectType.JUMP_BOOST);

                // Additional check: if player is supposed to be falling but isn't
                boolean shouldBeFalling = !isPlayerFalling && airMoves > 30;

                if ((sustainedHorizontal && notFalling) || suspiciousUpward || shouldBeFalling) {
                    player.sendMessage("§c[AntiCheat] Suspicious flight pattern detected");
                    Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " suspicious flight pattern - Air moves: " + airMoves + " Velocity: " + velocity + " Falling: " + isPlayerFalling);
                    flag(player, "Flight");
                }
            }

            // Extreme air time check (but not if legitimately falling)
            if (airMoves > 40 && !isPlayerFalling) {
                player.sendMessage("§c[AntiCheat] Excessive air time detected");
                Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " excessive air time - " + airMoves + " moves");
                flag(player, "Flight");
            }
        }
    }

    private void checkMidAirJump(Player player, PlayerMoveEvent event, boolean inAir, double fromY, double toY, long now) {
        UUID uuid = player.getUniqueId();

        // Only check if player is moving upward in air
        if (!inAir || fromY >= toY || Math.abs(toY - fromY) <= 0.15) return;

        // Comprehensive exemption checks
        boolean recentlyDamaged = player.getNoDamageTicks() > 0;
        boolean hasJumpBoost = player.hasPotionEffect(PotionEffectType.JUMP_BOOST);
        boolean hasLevitation = player.hasPotionEffect(PotionEffectType.LEVITATION);
        boolean isWindCharged = player.getVelocity().length() > 1.0;
        boolean isRiding = player.isInsideVehicle();
        boolean isGliding = player.isGliding();
        boolean isSwimming = player.isSwimming();
        boolean isSprinting = player.isSprinting();

        // Check for climbing blocks
        Block blockAt = player.getLocation().getBlock();
        Material atType = blockAt.getType();
        boolean isClimbing = atType.name().contains("LADDER") ||
                           atType.name().contains("VINE") ||
                           atType.name().contains("SCAFFOLDING");

        // Check for bouncy blocks below
        Block blockBelow = player.getLocation().getBlock().getRelative(0, -1, 0);
        boolean onBouncyBlock = blockBelow.getType() == Material.SLIME_BLOCK ||
                               blockBelow.getType() == Material.HONEY_BLOCK;

        // Check for liquid movement
        boolean inLiquid = atType == Material.WATER || atType == Material.LAVA ||
                          blockAt.getRelative(0, 1, 0).getType() == Material.WATER;

        // Check if recently left ground (lag compensation)
        Long lastGround = lastGroundTime.get(uuid);
        boolean recentlyOnGround = lastGround != null && (now - lastGround) < 500; // 500ms tolerance

        // Check velocity for legitimate upward movement
        Vector velocity = player.getVelocity();
        boolean hasUpwardVelocity = velocity.getY() > 0.2;

        // Only flag if none of the exemptions apply
        if (!recentlyDamaged && !hasJumpBoost && !hasLevitation && !isWindCharged &&
            !isRiding && !isGliding && !isSwimming && !isSprinting && !isClimbing &&
            !onBouncyBlock && !inLiquid && !recentlyOnGround && !hasUpwardVelocity) {

            // Additional check: make sure this isn't just lag
            int airMoves = consecutiveAirMoves.getOrDefault(uuid, 0);
            if (airMoves > 3) { // Only flag after multiple air moves to avoid lag false positives
                player.sendMessage("§c[AntiCheat] Detected suspicious mid-air movement [debug: would flag]");
                Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " flagged for suspicious mid-air movement");
                // Note: Changed to just log instead of immediate ban for testing
            }
        }
    }

    private void checkSpeed(Player player, PlayerMoveEvent event, long now) {
        UUID uuid = player.getUniqueId();

        double fromX = event.getFrom().getX();
        double fromZ = event.getFrom().getZ();
        double toX = event.getTo().getX();
        double toZ = event.getTo().getZ();
        double distance = Math.sqrt(Math.pow(toX - fromX, 2) + Math.pow(toZ - fromZ, 2));

        long lastTime = lastMoveTime.getOrDefault(uuid, now);
        long timeElapsed = now - lastTime;

        // Lag compensation - ignore very small time differences
        if (timeElapsed < 20) return; // Ignore moves less than 20ms apart (improved)

        // Prevent division by zero
        if (timeElapsed == 0) return;

        // Calculate speed with better timing
        double speed = distance / (timeElapsed / 1000.0); // blocks per second

        // Quick obvious speedhack check (before complex calculations)
        if (speed > 12.0 && !player.isGliding() && !player.isInsideVehicle()) {
            player.sendMessage("§c[AntiCheat] Extreme Speed detected - Speed: " + String.format("%.2f", speed));
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " Extreme Speed - " + speed + " blocks/s");
            flag(player, "Speed");
            lastMoveTime.put(uuid, now);
            return;
        }

        // Track recent speeds for pattern detection
        List<Double> speeds = recentSpeeds.computeIfAbsent(uuid, k -> new ArrayList<>());
        speeds.add(speed);
        if (speeds.size() > 10) speeds.remove(0); // Keep last 10 speeds

        // Get all 1.21 speed modifiers
        double maxSpeed = calculateMaxAllowedSpeed(player);

        // Debug logging for speed detection
        if (speed > maxSpeed * 0.8) { // Log when approaching limits
            Bukkit.getLogger().info("[AntiCheatAIO] DEBUG Speed: " + player.getName() + " - Speed: " + String.format("%.2f", speed) + " Max: " + String.format("%.2f", maxSpeed) + " Ratio: " + String.format("%.2f", speed/maxSpeed));
        }

        // Enhanced speed detection with multiple checks
        boolean isSpeedHack = false;
        String violationType = "";

        // 1. Basic speed check with tighter tolerance
        if (speed > maxSpeed * 1.15 && timeElapsed > 40) { // Reduced tolerance to 15%
            isSpeedHack = true;
            violationType = "Basic Speed";
        }

        // 1.5. Simple obvious speedhack detection (new)
        if (speed > 8.0 && !player.isGliding() && !player.isInsideVehicle()) { // Obvious speedhack
            isSpeedHack = true;
            violationType = "Obvious Speed";
        }

        // 2. Consistent high speed detection (speedhack pattern)
        if (speeds.size() >= 5) {
            double avgSpeed = speeds.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            long consecutiveHighSpeeds = speeds.stream().mapToLong(s -> s > maxSpeed * 1.1 ? 1 : 0).sum();

            if (avgSpeed > maxSpeed * 1.1 && consecutiveHighSpeeds >= 4) {
                isSpeedHack = true;
                violationType = "Consistent Speed";
            }
        }

        // 3. Burst speed detection (sudden speed increases)
        if (speeds.size() >= 3) {
            double currentAvg = speeds.subList(speeds.size() - 3, speeds.size()).stream()
                .mapToDouble(Double::doubleValue).average().orElse(0.0);
            if (currentAvg > maxSpeed * 1.3) {
                isSpeedHack = true;
                violationType = "Burst Speed";
            }
        }

        // 4. Extreme speed detection (obvious hacks)
        if (speed > maxSpeed * 2.0) {
            isSpeedHack = true;
            violationType = "Extreme Speed";
        }

        // 5. Timer-based speed detection (too many fast moves)
        Long lastViolation = lastSpeedViolation.get(uuid);
        if (lastViolation != null && (now - lastViolation) < 1000 && speed > maxSpeed * 1.05) {
            isSpeedHack = true;
            violationType = "Timer Speed";
        }

        if (isSpeedHack) {
            player.sendMessage("§c[AntiCheat] " + violationType + " detected - Speed: " + String.format("%.2f", speed) + " Max: " + String.format("%.2f", maxSpeed));
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " " + violationType + " - Speed: " + speed + " Max: " + maxSpeed + " Avg: " + (speeds.size() > 0 ? speeds.stream().mapToDouble(Double::doubleValue).average().orElse(0.0) : 0.0));
            flag(player, "Speed");
            lastSpeedViolation.put(uuid, now);
        }

        lastMoveTime.put(uuid, now);
    }

    private double calculateMaxAllowedSpeed(Player player) {
        double maxSpeed = 4.317; // Precise base walking speed in Minecraft

        // Sprinting multiplier
        if (player.isSprinting()) {
            maxSpeed = 5.612; // Precise sprinting speed
        }

        // Potion effects
        if (player.hasPotionEffect(PotionEffectType.SPEED)) {
            PotionEffect speedEffect = player.getPotionEffect(PotionEffectType.SPEED);
            if (speedEffect != null) {
                int amplifier = speedEffect.getAmplifier() + 1;
                maxSpeed *= (1.0 + 0.2 * amplifier); // 20% per level
            }
        }

        if (player.hasPotionEffect(PotionEffectType.SLOWNESS)) {
            PotionEffect slownessEffect = player.getPotionEffect(PotionEffectType.SLOWNESS);
            if (slownessEffect != null) {
                int amplifier = slownessEffect.getAmplifier() + 1;
                maxSpeed *= Math.max(0.1, 1.0 - 0.15 * amplifier); // 15% reduction per level
            }
        }

        // Block-based speed modifications
        Block blockAt = player.getLocation().getBlock();
        Block blockBelow = player.getLocation().getBlock().getRelative(0, -1, 0);
        Material atType = blockAt.getType();
        Material belowType = blockBelow.getType();

        // Ice blocks (1.21 includes all ice variants) - reduced multipliers
        if (belowType.name().contains("ICE") || belowType == Material.BLUE_ICE ||
            belowType == Material.PACKED_ICE || belowType == Material.FROSTED_ICE) {
            if (belowType == Material.BLUE_ICE) {
                maxSpeed *= 2.2; // Blue ice (reduced from 2.9)
            } else if (belowType == Material.PACKED_ICE) {
                maxSpeed *= 2.0; // Packed ice (reduced from 2.5)
            } else {
                maxSpeed *= 1.8; // Regular ice (reduced from 2.2)
            }
        }

        // Soul speed enchantment on soul blocks
        if (belowType == Material.SOUL_SAND || belowType == Material.SOUL_SOIL) {
            // Check for soul speed enchantment (approximate)
            maxSpeed *= 1.8; // Assume max soul speed for now
        }

        // Liquid movement
        if (atType == Material.WATER || atType == Material.LAVA) {
            maxSpeed *= 0.8;

            // Depth strider enchantment (approximate)
            if (atType == Material.WATER) {
                maxSpeed *= 1.5; // Assume depth strider
            }
        }

        // 1.21 specific blocks
        if (atType == Material.COBWEB) maxSpeed *= 0.25; // Cobweb slowdown
        if (atType == Material.POWDER_SNOW) maxSpeed *= 0.4; // Powder snow
        if (atType == Material.HONEY_BLOCK) maxSpeed *= 0.4; // Honey block
        if (belowType == Material.SLIME_BLOCK) maxSpeed *= 1.2; // Slight speed boost

        // Special movement modes (reduced limits)
        if (player.isGliding()) return 20.0; // Elytra flight (reduced)
        if (player.isSwimming()) return 6.0; // Swimming (reduced)
        if (player.isInsideVehicle()) return 15.0; // Vehicles (reduced)

        // Wind charge effects (1.21) - reduced limits
        Vector velocity = player.getVelocity();
        if (velocity != null && velocity.length() > 2.5) return 25.0; // Wind charge (reduced and higher threshold)

        // Riptide trident (high velocity in rain/water) - reduced limits
        if (player.isInWater() || player.getWorld().hasStorm()) {
            if (velocity != null && velocity.length() > 2.0) return 15.0; // Riptide effect (reduced)
        }

        return maxSpeed;
    }

    private void flag(Player player, String check) {
        UUID uuid = player.getUniqueId();
        Map<String, Integer> playerViolations = violations.computeIfAbsent(uuid, k -> new HashMap<>());
        int v = playerViolations.getOrDefault(check, 0) + 1;
        playerViolations.put(check, v);
        if (v == 10) {
            player.sendMessage("§e[AntiCheat] Warning: " + check + " (" + v + ")");
        } else if (v == 20) {
            player.kick(Component.text("Kicked for cheating: " + check));
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " kicked for " + check);
        } else if (v >= 40) {
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " would be banned for " + check);
        }
    }

    private boolean shouldBypass(Player player) {
        return player.hasMetadata("bypass_anticheat");
    }
}