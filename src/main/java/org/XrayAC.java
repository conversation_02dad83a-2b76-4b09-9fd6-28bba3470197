package org;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.entity.Player;
import org.bukkit.Material;
import org.bukkit.Bukkit;
import java.util.*;

public class XrayAC implements Listener {
    private final Map<UUID, Map<String, Integer>> violations = new HashMap<>();
    private final Map<UUID, List<Long>> oreMinedTimestamps = new HashMap<>();
    private final Map<UUID, List<Long>> diamondTimestamps = new HashMap<>();
    private final Map<UUID, List<Long>> ancientDebrisTimestamps = new HashMap<>();
    private final Map<UUID, Integer> totalBlocksBroken = new HashMap<>();
    private final Map<UUID, Integer> oresBroken = new HashMap<>();

    private final Set<Material> ores = new HashSet<>(Arrays.asList(
            Material.DIAMOND_ORE, Material.DEEPSLATE_DIAMOND_ORE, Material.ANCIENT_DEBRIS, Material.EMERALD_ORE, Material.DEEPSLATE_EMERALD_ORE,
            Material.GOLD_ORE, Material.DEEPSLATE_GOLD_ORE, Material.IRON_ORE, Material.DEEPSLATE_IRON_ORE, Material.LAPIS_ORE, Material.DEEPSLATE_LAPIS_ORE,
            Material.REDSTONE_ORE, Material.DEEPSLATE_REDSTONE_ORE, Material.COAL_ORE, Material.DEEPSLATE_COAL_ORE
    ));

    private final Set<Material> valuableOres = new HashSet<>(Arrays.asList(
            Material.DIAMOND_ORE, Material.DEEPSLATE_DIAMOND_ORE, Material.ANCIENT_DEBRIS,
            Material.EMERALD_ORE, Material.DEEPSLATE_EMERALD_ORE
    ));

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        if (shouldBypass(player)) return;
        UUID uuid = player.getUniqueId();
        Material type = event.getBlock().getType();
        long now = System.currentTimeMillis();

        // Track all blocks broken for ratio analysis
        totalBlocksBroken.put(uuid, totalBlocksBroken.getOrDefault(uuid, 0) + 1);

        if (!ores.contains(type)) return;

        // Track ores broken
        oresBroken.put(uuid, oresBroken.getOrDefault(uuid, 0) + 1);

        // --- Enhanced Ore Exposure Check ---
        boolean visible = isOreExposed(event.getBlock());
        if (!visible) {
            player.sendMessage("§c[AntiCheat] Suspicious ore mining detected");
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " mined unexposed " + type + " at " + event.getBlock().getLocation());
            flag(player, "Xray-OreExposure");
        }

        // --- Diamond-Specific Detection (Adjusted for legitimate mining) ---
        if (type == Material.DIAMOND_ORE || type == Material.DEEPSLATE_DIAMOND_ORE) {
            List<Long> diamondTimes = diamondTimestamps.computeIfAbsent(uuid, k -> new ArrayList<>());
            diamondTimes.add(now);
            diamondTimes.removeIf(t -> now - t > 10 * 60 * 1000); // 10 minute window (increased)

            // Much more reasonable thresholds for good tools
            if (diamondTimes.size() > 60) { // More than 60 diamonds in 10 minutes (was 15 in 5)
                player.sendMessage("§c[AntiCheat] Excessive diamond mining detected");
                Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " mined " + diamondTimes.size() + " diamonds in 10 minutes");
                flag(player, "Xray-DiamondRush");
            }

            // More lenient streak detection
            if (diamondTimes.size() >= 8) { // Need more diamonds for streak detection
                List<Long> recent = diamondTimes.subList(Math.max(0, diamondTimes.size() - 8), diamondTimes.size());
                if (recent.get(recent.size() - 1) - recent.get(0) < 120000) { // 8 diamonds in 2 minutes (was 3 in 1)
                    player.sendMessage("§c[AntiCheat] Diamond streak detected");
                    flag(player, "Xray-DiamondStreak");
                }
            }
        }

        // --- Ancient Debris Detection ---
        if (type == Material.ANCIENT_DEBRIS) {
            List<Long> debrisTimes = ancientDebrisTimestamps.computeIfAbsent(uuid, k -> new ArrayList<>());
            debrisTimes.add(now);
            debrisTimes.removeIf(t -> now - t > 10 * 60 * 1000); // 10 minute window

            if (debrisTimes.size() > 8) { // More than 8 ancient debris in 10 minutes
                player.sendMessage("§c[AntiCheat] Excessive ancient debris mining detected");
                flag(player, "Xray-DebrisRush");
            }
        }

        // --- Ore Ratio Analysis (More lenient) ---
        int totalBlocks = totalBlocksBroken.getOrDefault(uuid, 1);
        int totalOres = oresBroken.getOrDefault(uuid, 0);

        if (totalBlocks > 500) { // Only check after much more mining (was 100)
            double oreRatio = (double) totalOres / totalBlocks;

            // More reasonable ratio for good tools and luck
            if (oreRatio > 0.25) { // More than 25% ores (was 15%)
                player.sendMessage("§c[AntiCheat] Suspicious ore ratio detected");
                Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " ore ratio: " + String.format("%.2f", oreRatio * 100) + "% (" + totalOres + "/" + totalBlocks + ")");
                flag(player, "Xray-OreRatio");
            }
        }

        // --- General Ore Streaking (Reasonable threshold) ---
        List<Long> times = oreMinedTimestamps.computeIfAbsent(uuid, k -> new ArrayList<>());
        times.add(now);
        times.removeIf(t -> now - t > 15 * 60 * 1000); // 15 min window (increased)
        if (times.size() > 80) { // 80 ores in 15 minutes (much more reasonable)
            player.sendMessage("§c[AntiCheat] Ore streak detected");
            flag(player, "Xray-OreStreak");
        }

        // --- Valuable Ore Focus Detection ---
        if (valuableOres.contains(type)) {
            long valuableOreCount = times.stream().mapToLong(t -> {
                // Count only valuable ores in recent times (simplified check)
                return 1; // This is a simplified version - in practice you'd track ore types
            }).sum();

            if (times.size() > 10 && (double) times.size() / totalBlocks > 0.1) {
                player.sendMessage("§c[AntiCheat] Valuable ore focus detected");
                flag(player, "Xray-ValuableFocus");
            }
        }

        // --- Enhanced Mining Angle Detection ---
        double pitch = player.getLocation().getPitch();
        float yaw = player.getLocation().getYaw();
        boolean diagonal = (Math.abs((yaw % 90) - 45) < 15); // Slightly more lenient
        boolean straightDown = Math.abs(pitch) > 75; // Mining straight down

        if ((diagonal || straightDown) && !visible && valuableOres.contains(type)) {
            player.sendMessage("§c[AntiCheat] Suspicious mining angle detected");
            flag(player, "Xray-MiningAngle");
        }
    }

    private boolean isOreExposed(org.bukkit.block.Block block) {
        // Enhanced exposure check - check more directions and further out
        for (int dx = -2; dx <= 2; dx++) {
            for (int dy = -2; dy <= 2; dy++) {
                for (int dz = -2; dz <= 2; dz++) {
                    if (dx == 0 && dy == 0 && dz == 0) continue;

                    // Check if within reasonable mining distance
                    if (Math.abs(dx) + Math.abs(dy) + Math.abs(dz) <= 3) {
                        Material adj = block.getRelative(dx, dy, dz).getType();
                        if (adj == Material.AIR || adj == Material.CAVE_AIR || adj == Material.WATER ||
                            adj == Material.LAVA || adj.name().contains("LEAVES")) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private void flag(Player player, String check) {
        UUID uuid = player.getUniqueId();
        Map<String, Integer> playerViolations = violations.computeIfAbsent(uuid, k -> new HashMap<>());
        int v = playerViolations.getOrDefault(check, 0) + 1;
        playerViolations.put(check, v);
        if (v == 10) {
            player.sendMessage("§e[AntiCheat] Warning: " + check + " (" + v + ")");
        } else if (v == 20) {
            player.kickPlayer("Kicked for cheating: " + check);
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " kicked for " + check);
        } else if (v >= 40) {
            Bukkit.getLogger().info("[AntiCheatAIO] " + player.getName() + " would be banned for " + check);
        }
    }

    private boolean shouldBypass(Player player) {
        return player.hasMetadata("bypass_anticheat");
    }
}